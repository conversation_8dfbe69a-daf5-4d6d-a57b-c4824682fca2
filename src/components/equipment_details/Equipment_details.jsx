import React, { useEffect, useState } from 'react';
import RecommendationsSection from '../company_spotlight/Recommendations_section.jsx';
import { useParams } from 'react-router-dom';
import NeedInformation from '../home/<USER>';
import ReclamationProvider from '../../shared/context/Reclamation_context.jsx';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading.jsx';

import { index } from '../../shared/helpers/Algolia_helper.js';
import { useSearchContext } from '../../shared/context/Search_context.jsx';
import RenderIf from '../../shared/components/Render_if.jsx';
import SignInModal from '../../shared/components/modals/Sign_in_modal.jsx';
import { getCookies } from '../../shared/helpers/Cookies.js';
import ProjectProvider from '../../shared/context/Project_context.jsx';
import CreditCheckFormProvider from '../../shared/context/Credit_check_form_context.jsx';
import BookingModal from '../../shared/components/modals/Booking_modal.jsx';
import { useLodger } from '../../shared/context/Lodger_context.jsx';
import ScrollToTop from '../../shared/components/Scroll_to_top.jsx';
import BreadCrumb from '../../shared/components/Bread_crumb.jsx';
import { Box, Container } from '@mui/material';
import EquipmentDetailsPageSection from '../../shared/components/equipment/Equipment_details_page_section.jsx';
import EquipmentDetailsPageImageSection from '../../shared/components/equipment/Equipment_details_page_image_section.jsx';
import DescriptionPanelSection from '../../shared/components/equipment/Description_pannel_section.jsx';

export default function EquipmentDetails({
  t,
  detectedLanguage,
  setShowFPModal,
  showFPModal,
  signIn,
  item,
  role
}) {
  const params = useParams();
  const { getEquipperById } = useSearchContext();

  // Extract equipment ID and equipper ID from params.id (format: equipmentId_equipperId)
  const equipmentId = item
    ? item.objectID
    : params.id?.includes('_')
    ? params.id.split('_')[0]
    : params.id;
  const equipperId = item
    ? item.equipper_id
    : params.id?.includes('_')
    ? params.id.split('_')[1]
    : null;
  const { GetLodgerPersonalInfo } = useLodger();
  const [equipper, setEquipper] = useState(null);
  const [showSignIn, setShowSignIn] = useState(false);
  const [equipment, setEquipment] = useState(null);

  // Debug equipment state changes
  useEffect(() => {
    console.log('🔍 EQUIPMENT STATE CHANGED:', equipment);
  }, [equipment]);

  const [lodger, setLodger] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [show, setShow] = useState();
  const token = getCookies('token');

  const handleShowBooking = () => {
    setShow(!show);
  };

  useEffect(() => {
    async function fetchData() {
      const { status, data } = await GetLodgerPersonalInfo();
      if (status === 200) {
        setLodger(data);
      }
    }

    if (token) {
      fetchData();
    }
  }, [token]);

  useEffect(() => {
    async function fetchEquipmentData() {
      setIsLoading(true);
      console.log('🚀 STARTING EQUIPMENT FETCH');
      console.log('📋 equipmentId:', equipmentId);
      console.log('📋 equipperId:', equipperId);
      console.log('📋 item prop:', item);

      try {
        // First, try to get equipper data (but don't let this block equipment fetching)
        if (equipperId) {
          try {
            const { status, data } = await getEquipperById(equipperId);
            console.log('🏢 Equipper API response:', { status, data });
            if (status === 200 && data) {
              setEquipper(data);
            }
          } catch (equipperError) {
            console.warn('⚠️ Failed to fetch equipper data:', equipperError);
          }
        } else {
          console.log('⚠️ No equipperId found, skipping equipper fetch');
        }

        // Always try to get equipment data, regardless of equipper status
        // If item prop is provided, use it directly
        if (item) {
          console.log('📥 SETTING EQUIPMENT FROM ITEM PROP:', item);
          setEquipment(item);
        } else {
          // Otherwise, search for equipment by ID
          console.log('🔍 Searching Algolia with equipmentId:', equipmentId);

          if (!equipmentId || equipmentId.trim() === '') {
            console.log('❌ Invalid equipmentId, cannot search');
            setEquipment(null);
            return;
          }

          const { hits } = await index.search(equipmentId);
          console.log('🔍 Algolia search response:', {
            hits,
            query: equipmentId,
            hitsCount: hits?.length || 0
          });

          if (hits && hits.length > 0) {
            console.log('📥 SETTING EQUIPMENT FROM SEARCH HITS[0]:', hits[0]);
            setEquipment(hits[0]);
          } else {
            // Try alternative search strategies
            console.log('🔄 No direct hits, trying alternative search...');

            // Try searching with objectID field specifically
            const alternativeSearch = await index.search('', {
              filters: `objectID:${equipmentId}`
            });
            console.log('� Alternative search response:', {
              hits: alternativeSearch.hits,
              query: `objectID:${equipmentId}`,
              hitsCount: alternativeSearch.hits?.length || 0
            });

            if (alternativeSearch.hits && alternativeSearch.hits.length > 0) {
              console.log(
                '📥 SETTING EQUIPMENT FROM ALTERNATIVE SEARCH:',
                alternativeSearch.hits[0]
              );
              setEquipment(alternativeSearch.hits[0]);
            } else {
              console.log(
                '�📥 SETTING EQUIPMENT TO NULL (no hits found in any search)'
              );
              setEquipment(null);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching equipment data:', error);
        console.log('📥 SETTING EQUIPMENT TO NULL (error occurred)');
        setEquipment(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchEquipmentData();
  }, [equipmentId, equipperId, item]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  // Don't render if essential data is missing
  if (!equipment) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <p>{t ? t('Equipment_not_found') : 'Equipment not found'}</p>
      </div>
    );
  }

  console.log('🎯 FINAL EQUIPMENT STATE BEFORE RENDER:', equipment);
  return (
    <ScrollToTop>
      <BreadCrumb
        t={t}
        items={[
          {
            label: `${
              detectedLanguage === 'fr' ? equipment?.name_fr : equipment?.name
            }  `
          }
        ]}
      />
      <Box
        sx={{
          bgcolor: 'background.paper',
          display: 'flex',
          justifyContent: 'center',
          width: '100%',
          flexDirection: { xs: 'column', md: 'row' },
          mt: 2
        }}
      >
        <Container disableGutters maxWidth="lg" sx={{ position: 'relative' }}>
          {/* Main Content - Two Column Layout */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              mt: 2
            }}
          >
            <Box sx={{ flex: 2, mr: { md: 2 }, mb: { xs: 2, md: 0 } }}>
              <EquipmentDetailsPageSection
                equipment={equipment}
                detectedLanguage={detectedLanguage}
                equipper={equipper}
                t={t}
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <EquipmentDetailsPageImageSection
                token={token}
                handleShowBooking={handleShowBooking}
                setShowSignIn={setShowSignIn}
                detectedLanguage={detectedLanguage}
                equipment={equipment}
                t={t}
                role={role}
              />
            </Box>
          </Box>

          {/* Description Sections */}
          <Box sx={{ mt: 2 }}>
            <DescriptionPanelSection
              equipment={equipment}
              detectedLanguage={detectedLanguage}
              t={t}
            />
          </Box>
        </Container>
      </Box>
      <RenderIf condition={equipment ? equipment.objectID : params.id}>
        <div className="row">
          <div className="col-lg-10 mx-auto">
            <div className="company-spotlight--similarProduct">
              <h2 className="t-header-h3 bold c-fake-black title-search m-4">
                {`${t('Related_products')}`}
              </h2>
              <RecommendationsSection
                objectID={equipment ? equipment.objectID : params.id}
              />
            </div>
          </div>
        </div>
      </RenderIf>
      <ReclamationProvider>
        <div
          className="need-for-information-equipements"
          style={{ marginTop: '32px' }}
        >
          <Container disableGutters maxWidth="lg">
            <NeedInformation t={t} />
          </Container>
        </div>
      </ReclamationProvider>

      <RenderIf condition={show && token}>
        <ProjectProvider>
          <CreditCheckFormProvider>
            <BookingModal
              handleClose={handleShowBooking}
              data={{
                ...equipment,
                ...lodger
              }}
              isOpen={show}
              t={t}
              hoursPicker={equipper?.work_hours}
              detectLanguage={detectedLanguage}
            />
          </CreditCheckFormProvider>
        </ProjectProvider>
      </RenderIf>
      <SignInModal
        setShowFPModal={setShowFPModal}
        showFPModal={showFPModal}
        setShow={setShowSignIn}
        show={showSignIn}
        signIn={signIn}
        t={t}
      />
    </ScrollToTop>
  );
}
